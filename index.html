<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--=============== FAVICON ===============-->
    <link rel="shortcut icon" href="./assets/img/favicon.png" type="image/x-icon">

    <!--=============== BOXICONS ===============-->
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>

    <!--=============== SWIPER CSS ===============-->
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css">
    
    <!--=============== CSS ===============-->
    <link rel="stylesheet" href="assets/css/styles.css">
    
    <!--=============== TIMELINE CSS ===============-->
    <link rel="stylesheet" href="assets/css/timeline.css">

    <!--=============== IMAGE MODAL CSS ===============-->
    <style>
        /* 图片弹窗样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .image-modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .modal-image {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .image-modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10000;
            transition: color 0.3s ease;
        }

        .image-modal-close:hover,
        .image-modal-close:focus {
            color: #ccc;
        }

        .image-modal-caption {
            color: #fff;
            text-align: center;
            margin-top: 15px;
            font-size: 16px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 增强整体大气感的额外样式 */
        body {
            background: linear-gradient(135deg, #13629b 0%, #0f4d7a 50%, #13629b 100%);
            background-attachment: fixed;
        }

        /* 为主要内容区域添加微妙的光晕效果 */
        .home__carousel {
            position: relative;
        }

        .home__carousel::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 20px;
            z-index: -1;
            pointer-events: none;
        }

        /* 增强滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
            border-radius: 4px;
            backdrop-filter: blur(10px);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.3));
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .image-modal-content {
                padding: 10px;
            }

            .image-modal-close {
                top: 10px;
                right: 15px;
                font-size: 30px;
            }

            .modal-image {
                max-height: 70vh;
            }
        }
    </style>

    <title id="page-title">加载中...</title>
</head>

<body>
    <!--=============== HEADER ===============-->
    <header class="header" id="header">
        <nav class="nav container">
            <a href="#" class="nav__logo" id="nav-logo">加载中...</a>
            <!-- 导航菜单 -->
            <div class="nav__menu">
                <ul class="nav__list">
                    <li class="nav__item">
                        <a href="#home" class="nav__link active-link">
                            <i class='bx bx-home'></i>
                            <span class="nav__text">集团官网</span>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#about" class="nav__link">
                            <i class='bx bx-building'></i>
                            <span class="nav__text">企业简介</span>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#contact" class="nav__link">
                            <i class='bx bx-phone'></i>
                            <span class="nav__text">联系客服</span>
                        </a>
                    </li>
                </ul>
            </div>
            <!-- 主题切换按钮 -->
            <i class='bx bx-moon change-theme' id="theme-button"></i>
        </nav>
    </header>

    <!--=============== MAIN ===============-->
    <main class="main">
        <!--=============== HOME ===============-->
        <section class="home section" id="home">
            <div class="home__container container grid">
                <div class="home__carousel">
                    <div class="carousel-container">
                        <div class="carousel-slides" id="carousel-slides">
                            <!-- 轮播图将通过JavaScript动态生成 -->
                            <div class="carousel-loading">加载中...</div>
                        </div>
                        <div class="carousel-dots" id="carousel-dots">
                            <!-- 圆点将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== ABOUT ===============-->
        <section class="about section" id="about">
            <div class="about__container grid">
                <div class="about__expandable">
                    <div class="expandable__item">
                        <div class="expandable__header" data-target="product-info">
                            <h3 class="expandable__title">产品信息</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="product-info">
                            <div class="expandable__body">
                                <div class="product-info-form">
                                    <div class="form-row">
                                        <label class="form-label">品名</label>
                                        <input type="text" class="form-input" id="product-name" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">规格</label>
                                        <input type="text" class="form-input" id="product-spec" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">产地</label>
                                        <input type="text" class="form-input" id="product-origin" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">执行标准</label>
                                        <input type="text" class="form-input" id="product-standard" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">包装规格</label>
                                        <input type="text" class="form-input" id="product-package" value="加载中..." readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="production-trace">
                            <h3 class="expandable__title">生产追溯</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="production-trace">
                            <div class="expandable__body">
                                <!-- 生产追溯内容 -->
                                <div class="production-trace-container">
                                    <!-- 基本信息卡片 -->
                                    <div class="trace-info-card">
                                        <div class="form-row">
                                            <label class="form-label">生产批号</label>
                                            <input type="text" class="form-input" id="production-batch-no" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">生产日期</label>
                                            <input type="text" class="form-input" id="production-date" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">保质期</label>
                                            <input type="text" class="form-input" id="expiration-date" value="加载中..." readonly>
                                        </div>
                                    </div>

                                    <!-- 时间线 -->
                                    <div class="timeline-container" id="timeline-container">
                                        <!-- 时间线将通过JavaScript动态生成 -->
                                        <div class="timeline-loading">加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="company-intro">
                            <h3 class="expandable__title">企业介绍</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="company-intro">
                            <div class="expandable__body">
                                <!-- 企业介绍内容将在这里显示 -->
                                <p>企业介绍详细内容...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!--=============== IMAGE MODAL ===============-->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div class="image-modal-caption" id="imageCaption"></div>
        </div>
    </div>

    <!--=============== FOOTER ===============-->
    <footer class="footer">
        <div class="footer__container container">
            <div class="footer__image-section">
                <div class="footer__logo-area">
                    <div class="footer__logo-text">鲲鹏数字化追溯平台</div>
                   <span class="footer__icp">鲁ICP备2022003686号 ********</span>
                </div>
            </div>
         
        </div>
    </footer>

    <!--=============== SCROLLREVEAL ===============-->
    <script src="assets/js/scrollreveal.min.js"></script>

    <!--=============== SWIPER JS ===============-->
    <script src="assets/js/swiper-bundle.min.js"></script>

    <!--=============== MIXITUP FILTER ===============-->
    <script src="assets/js/mixitup.min.js"></script>

    <!--=============== MAIN JS ===============-->
    <script src="assets/js/main.js"></script>

    <!--=============== API CALL SCRIPT ===============-->
    <script>
        // 更新页面标题的函数
        function updatePageTitle(apiData) {
            try {
                const entName = apiData.EntName || '未知企业';

                // 更新页面标题
                const pageTitleElement = document.getElementById('page-title');
                if (pageTitleElement) {
                    pageTitleElement.textContent = entName;
                }

                // 更新导航栏logo
                const navLogoElement = document.getElementById('nav-logo');
                if (navLogoElement) {
                    navLogoElement.textContent = entName;
                }

                console.log('✅ 页面标题已更新:', entName);

            } catch (error) {
                console.error('更新页面标题时发生错误:', error);
                const pageTitleElement = document.getElementById('page-title');
                const navLogoElement = document.getElementById('nav-logo');
                if (pageTitleElement) pageTitleElement.textContent = '加载失败';
                if (navLogoElement) navLogoElement.textContent = '加载失败';
            }
        }

        // 更新轮播图的函数
        function updateCarousel(apiData) {
            try {
                const carouselImages = apiData.CarouselImages || [];
                const entCode = apiData.EntCode || '05580002';

                console.log('轮播图数据:', carouselImages);
                console.log('企业代码:', entCode);

                const carouselSlidesContainer = document.getElementById('carousel-slides');
                const carouselDotsContainer = document.getElementById('carousel-dots');

                if (!carouselSlidesContainer || !carouselDotsContainer) {
                    console.error('找不到轮播图容器元素');
                    return;
                }

                // 清空现有内容
                carouselSlidesContainer.innerHTML = '';
                carouselDotsContainer.innerHTML = '';

                if (carouselImages.length === 0) {
                    carouselSlidesContainer.innerHTML = '<div class="carousel-loading">暂无轮播图</div>';
                    return;
                }

                // 生成轮播图片
                carouselImages.forEach((imageId, index) => {
                    const imageUrl = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${imageId}`;

                    const imgElement = document.createElement('img');
                    imgElement.src = imageUrl;
                    imgElement.alt = `轮播图${index + 1}`;
                    imgElement.className = 'carousel-img';
                    imgElement.style.cursor = 'pointer';
                    if (index === 0) {
                        imgElement.classList.add('active');
                    }

                    // 添加点击事件显示弹窗
                    imgElement.onclick = function() {
                        showImageModal(this.src, `轮播图${index + 1}`);
                    };

                    carouselSlidesContainer.appendChild(imgElement);

                    // 生成对应的圆点
                    const dotElement = document.createElement('span');
                    dotElement.className = 'dot';
                    dotElement.setAttribute('data-slide', index.toString());
                    if (index === 0) {
                        dotElement.classList.add('active');
                    }

                    carouselDotsContainer.appendChild(dotElement);
                });

                // 重新初始化轮播功能
                initializeCarousel();

                console.log('✅ 轮播图已更新，共', carouselImages.length, '张图片');

            } catch (error) {
                console.error('更新轮播图时发生错误:', error);
                const carouselSlidesContainer = document.getElementById('carousel-slides');
                if (carouselSlidesContainer) {
                    carouselSlidesContainer.innerHTML = '<div class="carousel-loading">轮播图加载失败</div>';
                }
            }
        }

        // 初始化轮播功能
        function initializeCarousel() {
            let currentSlide = 0;
            const slides = document.querySelectorAll('.carousel-img');
            const dots = document.querySelectorAll('.dot');
            const totalSlides = slides.length;

            if (totalSlides === 0) return;

            function showSlide(index) {
                // 移除所有active类
                slides.forEach(slide => slide.classList.remove('active'));
                dots.forEach(dot => dot.classList.remove('active'));

                // 添加active类到当前幻灯片和点
                if (slides[index]) slides[index].classList.add('active');
                if (dots[index]) dots[index].classList.add('active');
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }

            // 点击圆点切换图片
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    showSlide(currentSlide);
                });
            });

            // 清除之前的定时器（如果存在）
            if (window.carouselInterval) {
                clearInterval(window.carouselInterval);
            }

            // 自动轮播，每3秒切换一次
            window.carouselInterval = setInterval(nextSlide, 3000);
        }

        // 更新页面产品信息的函数
        function updateProductInfo(apiData) {
            try {
                // 获取产品名称
                const productName = apiData.Product || '未知产品';

                // 获取产品属性
                const productProperties = apiData.ProductProperties || {};

                // 更新页面元素
                const productNameElement = document.getElementById('product-name');
                const productSpecElement = document.getElementById('product-spec');
                const productOriginElement = document.getElementById('product-origin');
                const productStandardElement = document.getElementById('product-standard');
                const productPackageElement = document.getElementById('product-package');

                if (productNameElement) {
                    productNameElement.value = productName;
                }

                if (productSpecElement) {
                    productSpecElement.value = productProperties['基础信息_规格'] || '未知规格';
                }

                if (productOriginElement) {
                    productOriginElement.value = productProperties['基础信息_产地'] || '未知产地';
                }

                if (productStandardElement) {
                    productStandardElement.value = productProperties['基础信息_执行标准'] || '未知标准';
                }

                if (productPackageElement) {
                    productPackageElement.value = productProperties['基础信息_包装规格'] || '未知包装';
                }

                console.log('✅ 页面产品信息已更新');
                console.log('品名:', productName);
                console.log('规格:', productProperties['基础信息_规格']);
                console.log('产地:', productProperties['基础信息_产地']);
                console.log('执行标准:', productProperties['基础信息_执行标准']);
                console.log('包装规格:', productProperties['基础信息_包装规格']);

            } catch (error) {
                console.error('更新产品信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 更新生产追溯信息的函数
        function updateProductionTrace(apiData) {
            try {
                // 更新基本信息卡片
                const batchNoElement = document.getElementById('production-batch-no');
                const productionDateElement = document.getElementById('production-date');
                const expirationDateElement = document.getElementById('expiration-date');

                if (batchNoElement) {
                    batchNoElement.value = apiData.ProductionBatchNo || '未知批号';
                }

                if (productionDateElement) {
                    productionDateElement.value = apiData.ProductionDate || '未知日期';
                }

                if (expirationDateElement) {
                    expirationDateElement.value = apiData.ExpirationDate || '未知日期';
                }

                // 动态生成时间线
                generateDynamicTimeline(apiData);

                console.log('✅ 生产追溯信息已更新');
                console.log('生产批号:', apiData.ProductionBatchNo);
                console.log('生产日期:', apiData.ProductionDate);
                console.log('保质期:', apiData.ExpirationDate);

            } catch (error) {
                console.error('更新生产追溯信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const traceElements = ['production-batch-no', 'production-date', 'expiration-date'];
                traceElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 动态生成时间线的函数
        function generateDynamicTimeline(apiData) {
            try {
                const timelineContainer = document.getElementById('timeline-container');
                if (!timelineContainer) {
                    console.error('找不到时间线容器');
                    return;
                }

                // 获取生命周期节点和数据
                const lifeCycleNodes = apiData.LifeCycleNodes || [];
                const lifeCycles = apiData.LifeCycles || {};
                const entCode = apiData.EntCode || '05580002';

                console.log('LifeCycleNodes:', lifeCycleNodes);
                console.log('LifeCycles:', lifeCycles);

                // 清空现有内容
                timelineContainer.innerHTML = '';

                if (lifeCycleNodes.length === 0) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">暂无时间线数据</div>';
                    return;
                }

                // 遍历每个生命周期节点
                lifeCycleNodes.forEach((node, index) => {
                    const nodeName = node.NodeName || `节点${index + 1}`;
                    const properties = node.Properties || [];

                    // 获取开始时间
                    const startTimeKey = `${nodeName}_开始时间`;
                    const startTime = lifeCycles[startTimeKey] || '未知时间';

                    // 创建时间线项目
                    const timelineItem = document.createElement('div');
                    timelineItem.className = 'timeline-item';
                    timelineItem.id = `timeline-${nodeName.replace(/\s+/g, '-')}`;

                    // 创建时间线日期
                    const timelineDate = document.createElement('div');
                    timelineDate.className = 'timeline-date';
                    timelineDate.innerHTML = `<span>${startTime}</span> ${nodeName}`;

                    // 创建时间线内容
                    const timelineContent = document.createElement('div');
                    timelineContent.className = 'timeline-content';

                    const timelineDetails = document.createElement('div');
                    timelineDetails.className = 'timeline-details';

                    // 遍历属性
                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;

                        if (!propertyName || propertyName === '开始时间') return; // 跳过开始时间属性

                        // 构建查找键：NodeName_PropertyName（去掉冒号）
                        const cleanPropertyName = propertyName.replace(/：$/, '');
                        const lookupKey = `${nodeName}_${cleanPropertyName}`;
                        let value = lifeCycles[lookupKey];

                        // 如果没找到，尝试带冒号的版本
                        if (!value && propertyName.endsWith('：')) {
                            const lookupKeyWithColon = `${nodeName}_${propertyName}`;
                            value = lifeCycles[lookupKeyWithColon];
                        }

                        if (!value) {
                            console.log(`未找到数据: ${lookupKey}`);
                            return; // 如果没有值，跳过
                        }

                        // 创建详情行
                        const detailRow = document.createElement('div');
                        detailRow.className = 'detail-row';

                        const detailLabel = document.createElement('span');
                        detailLabel.className = 'detail-label';
                        detailLabel.textContent = propertyName.endsWith('：') ? propertyName : `${propertyName}：`;

                        const detailValue = document.createElement('span');
                        detailValue.className = 'detail-value';

                        // 根据属性类型处理值
                        if (propertyType === 7) {
                            // 图片类型
                            const img = document.createElement('img');
                            img.src = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${value}`;
                            img.alt = cleanPropertyName;
                            img.style.maxWidth = '200px';
                            img.style.maxHeight = '150px';
                            img.style.objectFit = 'contain';
                            img.style.border = '1px solid #ddd';
                            img.style.borderRadius = '4px';
                            img.style.marginTop = '5px';
                            img.style.cursor = 'pointer';

                            // 添加点击放大功能
                            img.onclick = function() {
                                showImageModal(this.src, cleanPropertyName);
                            };

                            // 添加加载错误处理
                            img.onerror = function() {
                                this.style.display = 'none';
                                const errorText = document.createElement('span');
                                errorText.textContent = '图片加载失败';
                                errorText.style.color = '#999';
                                errorText.style.fontStyle = 'italic';
                                detailValue.appendChild(errorText);
                            };

                            detailValue.appendChild(img);
                        } else {
                            // 文本类型
                            detailValue.textContent = value;
                        }

                        detailRow.appendChild(detailLabel);
                        detailRow.appendChild(detailValue);
                        timelineDetails.appendChild(detailRow);
                    });

                    // 只有当有内容时才添加时间线项目
                    if (timelineDetails.children.length > 0) {
                        timelineContent.appendChild(timelineDetails);
                        timelineItem.appendChild(timelineDate);
                        timelineItem.appendChild(timelineContent);
                        timelineContainer.appendChild(timelineItem);
                    }
                });

                console.log('✅ 动态时间线已生成，共', lifeCycleNodes.length, '个节点');

            } catch (error) {
                console.error('生成动态时间线时发生错误:', error);
                const timelineContainer = document.getElementById('timeline-container');
                if (timelineContainer) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">时间线生成失败</div>';
                }
            }
        }

        // 更新企业介绍的函数
        async function updateCompanyIntro(apiData) {
            try {
                // 获取企业介绍的docId
                const entPublicize = apiData.EntPublicize || {};
                const companyIntroDocId = entPublicize['企业介绍'];
                const entCode = apiData.EntCode || '05580002';

                console.log('企业介绍docId:', companyIntroDocId);

                if (!companyIntroDocId) {
                    console.log('未找到企业介绍docId');
                    const companyIntroElement = document.querySelector('#company-intro .expandable__body');
                    if (companyIntroElement) {
                        companyIntroElement.innerHTML = '<p>暂无企业介绍信息</p>';
                    }
                    return;
                }

                // 调用企业介绍接口
                const introUrl = `https://www.kunpeng360.com/CustomerQuery/DocHtml?t=${entCode}&docId=${companyIntroDocId}`;
                console.log('正在获取企业介绍:', introUrl);

                // 通过代理服务器调用企业介绍接口
                const proxyIntroUrl = `http://localhost:3000/api/company-intro?t=${entCode}&docId=${companyIntroDocId}`;

                const response = await fetch(proxyIntroUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const htmlContent = await response.text();
                console.log('企业介绍HTML内容:', htmlContent);

                // 更新企业介绍内容
                const companyIntroElement = document.querySelector('#company-intro .expandable__body');
                if (companyIntroElement) {
                    companyIntroElement.innerHTML = htmlContent;

                    // 处理企业介绍中的图片URL和点击事件
                    const images = companyIntroElement.querySelectorAll('img');
                    images.forEach((img, index) => {
                        // 修复图片URL - 如果是相对路径，添加完整域名
                        let originalSrc = img.getAttribute('src');
                        let fixedSrc = originalSrc;

                        if (originalSrc.startsWith('/CustomerQuery/')) {
                            fixedSrc = `https://www.kunpeng360.com${originalSrc}`;
                        } else if (originalSrc.startsWith('CustomerQuery/')) {
                            fixedSrc = `https://www.kunpeng360.com/${originalSrc}`;
                        }

                        // 设置修复后的URL
                        img.src = fixedSrc;

                        // 添加图片加载错误处理
                        img.onerror = function() {
                            console.log('图片加载失败:', this.src);
                            // 如果加载失败，尝试通过代理加载
                            const imageId = originalSrc.match(/imageId=([^&]+)/);
                            const t = originalSrc.match(/t=([^&]+)/);
                            if (imageId && t) {
                                this.src = `http://localhost:3000/api/image?t=${t[1]}&imageId=${imageId[1]}`;
                            }
                        };

                        // 添加图片加载成功处理
                        img.onload = function() {
                            console.log('图片加载成功:', this.src);
                        };

                        img.style.cursor = 'pointer';
                        img.onclick = function() {
                            // 如果图片有onclick属性，执行原有的showPreview函数
                            if (this.hasAttribute('onclick')) {
                                const onclickValue = this.getAttribute('onclick');
                                if (onclickValue.includes('showPreview')) {
                                    // 提取showPreview的参数
                                    const match = onclickValue.match(/showPreview\('([^']+)',\s*true\)/);
                                    if (match) {
                                        const imageUrl = match[1];
                                        showImageModal(`https://www.kunpeng360.com${imageUrl}`, '企业介绍图片');
                                    }
                                }
                            } else {
                                // 否则使用图片的src
                                showImageModal(this.src, '企业介绍图片');
                            }
                        };
                    });
                }

                console.log('✅ 企业介绍已更新');

            } catch (error) {
                console.error('更新企业介绍时发生错误:', error);
                const companyIntroElement = document.querySelector('#company-intro .expandable__body');
                if (companyIntroElement) {
                    companyIntroElement.innerHTML = '<p>企业介绍加载失败</p>';
                }
            }
        }

        // 调用昆鹏360接口的函数（通过本地代理服务器）
        async function callKunpengAPI() {
            const proxyUrl = 'http://localhost:3000/api';

            try {
                console.log('正在通过代理服务器调用API:', proxyUrl);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('代理服务器响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('=== API调用结果 ===');
                console.log('完整响应:', result);

                if (result.success) {
                    console.log('=== 原始API返回数据 ===');
                    console.log(result.rawData);

                    console.log('=== 解析后的数据 ===');
                    console.log(result.data);

                    console.log('=== API响应状态码 ===');
                    console.log(result.statusCode);

                    console.log('=== API响应头 ===');
                    console.log(result.headers);

                    // 🎯 关键：更新页面所有信息
                    updatePageTitle(result.data);
                    updateCarousel(result.data);
                    updateProductInfo(result.data);
                    updateProductionTrace(result.data);
                    updateCompanyIntro(result.data);

                } else {
                    console.error('API调用失败:', result.error);
                    // 显示错误状态
                    const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                    elements.forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.value = 'API调用失败';
                        }
                    });
                }

                return result;

            } catch (error) {
                console.error('调用代理服务器时发生错误:', error);
                console.error('错误详情:', error.message);
                console.log('请确保代理服务器正在运行 (node api-server.js)');

                // 显示错误状态
                const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '连接失败';
                    }
                });

                return null;
            }
        }

        // 页面加载完成后自动调用API
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始调用API...');

            // 调用真实API
            callKunpengAPI().then(result => {
                if (!result || !result.success) {
                    console.error('API调用失败，无法获取数据');
                    // 显示错误状态
                    showErrorState('API调用失败');
                }
            }).catch(error => {
                console.error('API调用出错:', error);
                showErrorState('连接失败');
            });
        });

        // 显示错误状态的函数
        function showErrorState(errorMessage) {
            const elements = [
                'product-name', 'product-spec', 'product-origin',
                'product-standard', 'product-package', 'production-batch-no',
                'production-date', 'expiration-date'
            ];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = errorMessage;
                }
            });
        }

        // 显示图片弹窗
        function showImageModal(imageSrc, caption) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const imageCaption = document.getElementById('imageCaption');

            modal.style.display = 'block';
            modalImage.src = imageSrc;
            imageCaption.textContent = caption || '图片详情';

            // 添加淡入动画
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
        }

        // 隐藏图片弹窗
        function hideImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 初始化图片弹窗事件
        function initImageModal() {
            const modal = document.getElementById('imageModal');
            const closeBtn = document.querySelector('.image-modal-close');

            // 点击关闭按钮
            closeBtn.onclick = hideImageModal;

            // 点击弹窗背景关闭
            modal.onclick = function(event) {
                if (event.target === modal) {
                    hideImageModal();
                }
            };

            // 按ESC键关闭
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && modal.style.display === 'block') {
                    hideImageModal();
                }
            });

            // 添加CSS过渡效果
            modal.style.transition = 'opacity 0.3s ease';
        }

        // 页面加载完成后初始化弹窗
        document.addEventListener('DOMContentLoaded', function() {
            initImageModal();
        });

        // 也可以手动调用
        window.callKunpengAPI = callKunpengAPI;
        window.showImageModal = showImageModal;
        window.hideImageModal = hideImageModal;
    </script>
</body>

</html>